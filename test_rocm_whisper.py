#!/usr/bin/env python3
"""
Test script to verify that <PERSON><PERSON><PERSON> works with ROCm
"""

import sys
import torch
from AIModules.stt import OpenAIWhisper, FastWhisper

def test_pytorch_rocm():
    """Test PyTorch ROCm setup"""
    print("=== PyTorch ROCm Test ===")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"Device count: {torch.cuda.device_count()}")
        print(f"Current device: {torch.cuda.current_device()}")
        print(f"Device name: {torch.cuda.get_device_name(0)}")
        
        # Test basic tensor operations on GPU
        try:
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y)
            print(f"GPU tensor operation successful: {z.shape}")
            print(f"GPU memory allocated: {torch.cuda.memory_allocated(0) / 1024**3:.2f} GB")
        except Exception as e:
            print(f"GPU tensor operation failed: {e}")
    else:
        print("No GPU detected")
    print()

def test_openai_whisper():
    """Test OpenAI Whisper with ROCm"""
    print("=== OpenAI Whisper Test ===")
    try:
        whisper = OpenAIWhisper()
        
        if torch.cuda.is_available():
            print(f"Model device: {next(whisper.model.parameters()).device}")
            print(f"GPU memory after model load: {torch.cuda.memory_allocated(0) / 1024**3:.2f} GB")
        
        print("OpenAI Whisper initialization successful!")
        return True
    except Exception as e:
        print(f"OpenAI Whisper initialization failed: {e}")
        return False
    print()

def test_faster_whisper():
    """Test faster-whisper (may not work with ROCm)"""
    print("=== Faster Whisper Test ===")
    try:
        whisper = FastWhisper()
        print("Faster Whisper initialization successful!")
        return True
    except Exception as e:
        print(f"Faster Whisper initialization failed: {e}")
        return False
    print()

def main():
    print("Testing Whisper with ROCm support\n")
    
    # Test PyTorch ROCm
    test_pytorch_rocm()
    
    # Test OpenAI Whisper
    openai_success = test_openai_whisper()
    
    # Test faster-whisper
    faster_success = test_faster_whisper()
    
    print("=== Summary ===")
    print(f"PyTorch ROCm: {'✓' if torch.cuda.is_available() else '✗'}")
    print(f"OpenAI Whisper: {'✓' if openai_success else '✗'}")
    print(f"Faster Whisper: {'✓' if faster_success else '✗'}")
    
    if openai_success:
        print("\n✓ ROCm setup successful! OpenAI Whisper is working with GPU acceleration.")
    elif faster_success:
        print("\n⚠ Faster Whisper works but may be using CPU. OpenAI Whisper failed.")
    else:
        print("\n✗ Both Whisper implementations failed. Check your setup.")

if __name__ == "__main__":
    main()
