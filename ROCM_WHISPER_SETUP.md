# ROCm Whisper Setup

This document explains how faster-whisper has been configured to work with ROCm (AMD GPU acceleration).

## What was done

1. **Installed PyTorch with ROCm support**:
   - Uninstalled the CUDA version of PyTorch
   - Installed PyTorch 2.6.0 with ROCm 6.1 support
   - Verified GPU detection and tensor operations

2. **Updated STT module**:
   - Added OpenAI Whisper implementation alongside faster-whisper
   - OpenAI Whisper has better ROCm compatibility through PyTorch
   - Set OpenAI Whisper as the default implementation

3. **Fallback mechanism**:
   - faster-whisper falls back to CPU if GPU fails (due to CTranslate2 limitations)
   - OpenAI Whisper successfully uses GPU acceleration

## Current Status

✅ **Working**: OpenAI Whisper with GPU acceleration (3.1GB VRAM usage)
⚠️ **Limited**: faster-whisper falls back to CPU (CTranslate2 doesn't support ROCm)
✅ **PyTorch**: Full ROCm support with AMD Radeon RX 7800 XT

## Usage

The STT module automatically uses the best available implementation:

```python
from AIModules.stt import Whisper

# This will use OpenAI Whisper with GPU acceleration
whisper = Whisper()
text = whisper.recognise("audio_file.wav")
```

To explicitly choose an implementation:

```python
from AIModules.stt import OpenAIWhisper, FastWhisper

# Use OpenAI Whisper (recommended for ROCm)
whisper = OpenAIWhisper()

# Use faster-whisper (will fall back to CPU)
whisper = FastWhisper()
```

## Performance

- **OpenAI Whisper on GPU**: ~3.1GB VRAM, fast inference
- **faster-whisper on CPU**: Lower memory usage, slower inference

## Testing

Run the test script to verify your setup:

```bash
python test_rocm_whisper.py
```

## Technical Details

- **PyTorch version**: 2.6.0+rocm6.1
- **ROCm version**: 6.1 (compatible with ROCm 7.5.0 system)
- **GPU**: AMD Radeon RX 7800 XT
- **Whisper model**: turbo (large-v3-turbo equivalent)

## Troubleshooting

If you encounter issues:

1. Verify ROCm installation: `rocm-smi`
2. Check PyTorch ROCm: `python -c "import torch; print(torch.cuda.is_available())"`
3. Run the test script: `python test_rocm_whisper.py`

## Notes

- CTranslate2 (used by faster-whisper) doesn't have native ROCm support
- OpenAI Whisper uses PyTorch directly, which has excellent ROCm support
- The setup maintains backward compatibility with both implementations
