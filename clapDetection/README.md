# Wake Up, Daddy's Home

![Untitled design (1)](https://github.com/huwprosser/clap-detection/assets/16668357/890492ac-6636-4cf4-af62-427bf14b693a)



A convolutional neural network that classifies audio samples as MelSpectograms. Clap or not clap. Built live. Can be used for any audio binary classification. Please improve!

[NEW] Pretrained Model download [Here](https://drive.google.com/file/d/1o57-J436_OmcOgA-Vt3e3ontPPqA4gu_/view?usp=sharing)

Watch the stream [here:](https://www.youtube.com/watch?v=OBodG-6YEMo) 

Dataset (I highly recommend making your own): 
https://huggingface.co/datasets/dvncoder/clap-detection
