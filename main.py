from AIModules import listen, AIChatbot, stt, tts

# Path to tmp wav file
path = "tmp/tmp.wav"

# Activate libs
# Activate the listener
record = listen.recorder()
# activate Text to speech
fastWhisper = stt.FastWhisperAMD()
# activate AIChatbot
ollamaClient = AIChatbot.ollamaClient()
# activate tts
TTS = tts.TTS()

# Listen for someone talking and record it
frames = record.listen()

# Write the frames to a tmp WAV file 
record.toWAV(frames, path)

# Speech to text
sttOutoput = fastWhisper.recognise(path)

# chatbot personality
personality = ""

# send the text to the chatbot
AIChatOuput = ollamaClient.send(sttOutoput, personality)

print(sttOutoput)
print(AIChatOuput)

# TTS.say(AIChatOuput)

