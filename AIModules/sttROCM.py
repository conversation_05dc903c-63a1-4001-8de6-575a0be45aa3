import ctranslate2, transformers, librosa

class FastWhisperAMD:
    def __init__(self, model_dir_ct2):
        processor = transformers.WhisperProcessor.from_pretrained("openai/whisper-tiny")
        self.processor = processor
        self.model = ctranslate2.models.Whisper(model_dir_ct2, device="cuda")

    def recognise(self, path):
        audio, _ = librosa.load(path, sr=16000, mono=True)
        inputs = self.processor(audio, return_tensors="np", sampling_rate=16000)
        features = ctranslate2.StorageView.from_array(inputs.input_features)
        results = self.model.detect_language(features)
        prompt = self.processor.tokenizer.convert_tokens_to_ids(
            ["<|startoftranscript|>", results[0][0][0], "<|transcribe|>", "<|notimestamps|>"])
        output = self.model.translate_batch([features], prompt=[prompt])
        return output[0].sequences_ids