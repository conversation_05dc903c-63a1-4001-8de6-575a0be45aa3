import warnings
import torch
import whisper
from faster_whisper import WhisperModel

warnings.simplefilter(action="ignore", category=FutureWarning)
warnings.simplefilter(action="ignore", category=UserWarning)

class FastWhisper:
    def __init__(self):
        print("Whisper model loading...")

        # Try to use GPU with ROCm, but fall back to CPU if there are issues
        if torch.cuda.is_available():
            print(f"GPU detected: {torch.cuda.get_device_name(0)}")

            # Try different compute types that might work with ROCm
            gpu_compute_types = ["float32", "int8", "default"]

            for compute_type in gpu_compute_types:
                try:
                    print(f"Trying GPU with compute_type: {compute_type}")
                    self.model = WhisperModel("turbo", device="cuda", compute_type=compute_type)
                    print(f"Whisper model loaded on GPU with {compute_type}")
                    return
                except Exception as e:
                    print(f"GPU with {compute_type} failed: {e}")
                    continue

            # If all GPU attempts failed, fall back to CPU
            print("All GPU attempts failed, falling back to CPU")
            self.model = WhisperModel("turbo", device="cpu", compute_type="int8")
            print("Whisper model loaded on CPU")
        else:
            print("No GPU detected, using CPU")
            self.model = WhisperModel("turbo", device="cpu", compute_type="int8")
            print("Whisper model loaded on CPU")

    def recognise(self, path):
        segments, info = self.model.transcribe(path)

        text = ''

        for segment in segments:
            text = segment.text

        return text


class OpenAIWhisper:
    """
    Alternative Whisper implementation using OpenAI's original library
    which has better ROCm support through PyTorch.
    """
    def __init__(self):
        print("OpenAI Whisper model loading...")

        # Determine device
        if torch.cuda.is_available():
            self.device = "cuda"
            print(f"Using GPU: {torch.cuda.get_device_name(0)}")
        else:
            self.device = "cpu"
            print("Using CPU")

        # Load model - turbo is equivalent to large-v3-turbo
        try:
            self.model = whisper.load_model("turbo", device=self.device)
            print(f"OpenAI Whisper model loaded on {self.device}")
        except Exception as e:
            print(f"Failed to load turbo model ({e}), trying large-v3...")
            try:
                self.model = whisper.load_model("large-v3", device=self.device)
                print(f"OpenAI Whisper large-v3 model loaded on {self.device}")
            except Exception as e2:
                print(f"Failed to load large-v3 model ({e2}), falling back to base...")
                self.model = whisper.load_model("base", device=self.device)
                print(f"OpenAI Whisper base model loaded on {self.device}")

    def recognise(self, path):
        result = self.model.transcribe(path)
        return result["text"].strip()


# Use OpenAI Whisper by default for better ROCm compatibility
# You can change this to FastWhisper if you prefer faster-whisper
Whisper = OpenAIWhisper