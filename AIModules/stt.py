import warnings
from faster_whisper import WhisperModel

warnings.simplefilter(action="ignore", category=FutureWarning)
warnings.simplefilter(action="ignore", category=UserWarning)

class FastWhisper:
    def __init__(self):
        print("Whisper model loading...")
        self.model = WhisperModel("turbo", device="cpu", compute_type="int8")
        print("Whisper model loaded")

    def recognise(self, path):
        segments, info = self.model.transcribe(path)

        text = ''

        for segment in segments:
            text = segment.text

        return text

class FastWhisperAMD:
    def __init__(self, model_name="openai/whisper-medium.en"):
        import torch
        from transformers import pipeline
        print("Loading model on device:", torch.version.hip if torch.cuda.is_available() else "cpu")
        device = 0 if torch.cuda.is_available() else -1
        self.pipe = pipeline("automatic-speech-recognition",
                             model=model_name,
                             chunk_length_s=30,
                             device=device)

    def recognise(self, path):
        result = self.pipe(path)
        return result["text"]