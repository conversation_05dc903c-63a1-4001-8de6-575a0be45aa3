import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)
warnings.simplefilter(action='ignore', category=UserWarning)

import pyaudio
from silero_vad import load_silero_vad, get_speech_timestamps
import numpy as np
import wavio
import torch

class recorder: 
    def __init__(self):
        self.sample_rate = 16000
        self.frame_duration = 20 
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)  

        print("Silero model loading")
        self.sileroModel = load_silero_vad()
        print("Silero model loaded")

        print("Mic activating")
        self.audio = pyaudio.PyAudio()
        self.stream = self.audio.open(format=pyaudio.paInt16, channels=1,
            rate=self.sample_rate, input=True,
            frames_per_buffer=self.frame_size)
        print("Mic activated")

    def listen(self):
        buffer = []
        preBuffer = []
        frames = []
        speech_detected = False

        while True:
            data = self.stream.read(self.frame_size)
            
            buffer.append(data)

            if len(buffer) >= int(self.sample_rate / self.frame_size):
                audio_np = np.frombuffer(b''.join(buffer), dtype=np.int16).astype(np.float32) / 32768.0
                audio_tensor = torch.tensor(audio_np, dtype=torch.float32)

                speech_timestamps = get_speech_timestamps(audio_tensor, self.sileroModel)

                if speech_timestamps:
                    print("Speech detected")
                    frames = frames + buffer
                    speech_detected = True
                elif speech_detected:
                    print("Speech ended")
                    frames = preBuffer + frames
                    return frames
                else:
                    print("No speech detected")
                    preBuffer = buffer
                    
                buffer = []
    
    def toWAV(self, frames, path):
        audio_data = np.frombuffer(b''.join(frames), dtype=np.int16)
        wavio.write(path, audio_data, self.sample_rate, sampwidth=2)
        