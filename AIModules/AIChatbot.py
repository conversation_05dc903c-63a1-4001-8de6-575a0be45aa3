from ollama import Client

class ollamaClient:
    def __init__(self):
        self.ollamaClient = Client(host='http://*************:11434/')
        self.chat_history = []

    def send(self, message, personality):
        self.chat_history.append({'role': 'user', 'content': message})
        
        output = self.ollamaClient.chat(
            model='llama3.1:latest',
            messages=[
                {'role': 'system', 'content': personality},
                *self.chat_history 
            ]
        )
        
        response = output['message']['content']
        self.chat_history.append({'role': 'assistant', 'content': response})
        
        return response
    
    def DeleteChatHistory():
        self.chat_history = []